// Test different AI models
async function testModel(modelName) {
  try {
    console.log(`\n=== Testing model: ${modelName} ===`);
    
    const response = await fetch('http://localhost:3001/api/generate-ai-code-stream', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        prompt: 'Create a simple React component that displays "Hello World"',
        model: modelName,
        context: {}
      })
    });
    
    console.log('Response status:', response.status);
    
    if (!response.ok) {
      const errorData = await response.json();
      console.log('❌ Failed:', errorData);
      return false;
    }
    
    // Handle streaming response
    const reader = response.body?.getReader();
    const decoder = new TextDecoder();
    let hasError = false;
    let hasCode = false;
    
    if (reader) {
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;
        
        const chunk = decoder.decode(value);
        const lines = chunk.split('\n');
        
        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6));
              
              if (data.type === 'error') {
                console.log('❌ Stream error:', data.error);
                hasError = true;
              } else if (data.type === 'complete' && data.generatedCode) {
                hasCode = true;
                console.log('✅ Code generated successfully');
              }
            } catch (e) {
              // Ignore parse errors
            }
          }
        }
      }
    }
    
    if (hasError) {
      console.log('❌ Model failed with errors');
      return false;
    } else if (hasCode) {
      console.log('✅ Model works correctly');
      return true;
    } else {
      console.log('⚠️ Model completed but no code generated');
      return false;
    }
    
  } catch (error) {
    console.error('❌ Test error:', error.message);
    return false;
  }
}

async function testAllModels() {
  const models = [
    'google/gemini-2.5-pro',
    'anthropic/claude-sonnet-4-20250514',
    'openai/gpt-4',
    'openai/gemini-2.5-pro',
    'moonshotai/kimi-k2-instruct'
  ];
  
  console.log('Testing all available AI models...\n');
  
  const results = {};
  
  for (const model of models) {
    results[model] = await testModel(model);
    // Wait a bit between tests
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  console.log('\n=== SUMMARY ===');
  for (const [model, success] of Object.entries(results)) {
    console.log(`${success ? '✅' : '❌'} ${model}`);
  }
  
  const workingModels = Object.entries(results).filter(([_, success]) => success);
  if (workingModels.length > 0) {
    console.log(`\n推荐使用: ${workingModels[0][0]}`);
  } else {
    console.log('\n❌ 没有可用的模型，请检查 API 密钥配置');
  }
}

testAllModels();
