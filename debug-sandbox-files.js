// 调试沙箱文件状态
async function debugSandboxFiles() {
  try {
    console.log('获取沙箱文件列表...');
    
    const response = await fetch('http://localhost:3001/api/get-sandbox-files', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    console.log('获取文件 API 响应状态:', response.status);
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ 文件列表获取成功');
      console.log('文件数据:', JSON.stringify(data, null, 2));
      
      if (data.files) {
        console.log('\n📁 沙箱中的文件:');
        Object.keys(data.files).forEach(filePath => {
          const content = data.files[filePath];
          console.log(`\n--- ${filePath} ---`);
          console.log(`内容长度: ${content.length} 字符`);
          console.log(`内容预览: ${content.substring(0, 200)}...`);
        });
      }
      
      return true;
    } else {
      const errorData = await response.json();
      console.log('❌ 文件列表获取失败');
      console.log('错误数据:', errorData);
      return false;
    }
    
  } catch (error) {
    console.error('❌ 调试文件出错:', error.message);
    return false;
  }
}

async function checkViteStatus() {
  try {
    console.log('\n检查 Vite 开发服务器状态...');
    
    // 直接访问沙箱的 Vite 服务器
    const sandboxUrl = 'https://5173-i6whcsy74cilwjm7tubiw.e2b.app';
    const response = await fetch(sandboxUrl);
    
    console.log('Vite 服务器响应状态:', response.status);
    
    if (response.ok) {
      const html = await response.text();
      console.log('HTML 内容长度:', html.length);
      
      // 检查关键内容
      if (html.includes('Nano Banana')) {
        console.log('✅ 发现 Nano Banana 内容');
      } else if (html.includes('Gazoulab')) {
        console.log('✅ 发现 Gazoulab 内容');
      } else if (html.includes('Vite + React')) {
        console.log('⚠️ 显示默认 Vite + React 页面');
      } else if (html.includes('Sandbox Ready')) {
        console.log('❌ 显示 Sandbox Ready 页面');
      } else {
        console.log('❓ 显示未知内容');
      }
      
      // 显示 HTML 的前 500 字符
      console.log('\nHTML 内容预览:');
      console.log(html.substring(0, 500) + '...');
      
      return true;
    } else {
      console.log('❌ Vite 服务器无法访问');
      return false;
    }
    
  } catch (error) {
    console.error('❌ 检查 Vite 状态出错:', error.message);
    return false;
  }
}

async function forceRestartAndCheck() {
  try {
    console.log('\n强制重启 Vite 并检查...');
    
    const response = await fetch('http://localhost:3001/api/restart-vite', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({})
    });
    
    console.log('重启响应状态:', response.status);
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ Vite 重启成功');
      console.log('重启结果:', data);
      
      // 等待 5 秒让 Vite 完全启动
      console.log('等待 5 秒让 Vite 完全启动...');
      await new Promise(resolve => setTimeout(resolve, 5000));
      
      // 再次检查状态
      return await checkViteStatus();
    } else {
      const errorData = await response.json();
      console.log('❌ Vite 重启失败');
      console.log('错误数据:', errorData);
      return false;
    }
    
  } catch (error) {
    console.error('❌ 强制重启出错:', error.message);
    return false;
  }
}

async function runFullDiagnostics() {
  console.log('=== 沙箱完整诊断 ===\n');
  
  // 步骤 1: 检查文件
  const filesCheck = await debugSandboxFiles();
  
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  // 步骤 2: 检查 Vite 状态
  const viteCheck = await checkViteStatus();
  
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  // 步骤 3: 如果有问题，强制重启
  if (!viteCheck) {
    console.log('\n🔄 Vite 状态异常，尝试强制重启...');
    const restartCheck = await forceRestartAndCheck();
    
    console.log('\n=== 最终诊断结果 ===');
    console.log(`文件检查: ${filesCheck ? '✅ 成功' : '❌ 失败'}`);
    console.log(`Vite 检查: ${viteCheck ? '✅ 成功' : '❌ 失败'}`);
    console.log(`重启后检查: ${restartCheck ? '✅ 成功' : '❌ 失败'}`);
  } else {
    console.log('\n=== 诊断结果 ===');
    console.log(`文件检查: ${filesCheck ? '✅ 成功' : '❌ 失败'}`);
    console.log(`Vite 检查: ${viteCheck ? '✅ 成功' : '❌ 失败'}`);
  }
}

// 运行完整诊断
runFullDiagnostics();
