// Test AI code generation
async function testAIGeneration() {
  try {
    console.log('Testing AI code generation...');
    
    const response = await fetch('http://localhost:3001/api/generate-ai-code-stream', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        prompt: 'Create a simple React component that displays "Hello World" with a blue background',
        model: 'openai/gemini-2.5-pro',
        context: {}
      })
    });
    
    console.log('Response status:', response.status);
    
    if (!response.ok) {
      const errorData = await response.json();
      console.log('❌ AI generation failed:', errorData);
      return;
    }
    
    // Handle streaming response
    const reader = response.body?.getReader();
    const decoder = new TextDecoder();
    let generatedCode = '';
    let hasError = false;
    
    if (reader) {
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;
        
        const chunk = decoder.decode(value);
        const lines = chunk.split('\n');
        
        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6));
              
              if (data.type === 'error') {
                console.log('❌ Stream error:', data.error);
                hasError = true;
              } else if (data.type === 'complete') {
                generatedCode = data.generatedCode;
                console.log('✅ Generation complete');
              } else if (data.type === 'status') {
                console.log('Status:', data.message);
              }
            } catch (e) {
              // Ignore parse errors for non-JSON lines
            }
          }
        }
      }
    }
    
    if (hasError) {
      console.log('❌ AI generation had errors');
    } else if (generatedCode) {
      console.log('✅ AI generation successful');
      console.log('Generated code length:', generatedCode.length);
      console.log('Code preview:', generatedCode.substring(0, 200) + '...');
    } else {
      console.log('⚠️ AI generation completed but no code received');
    }
    
  } catch (error) {
    console.error('❌ AI generation test error:', error.message);
  }
}

testAIGeneration();
