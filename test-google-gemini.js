// 测试直接使用 Google Gemini API
async function testGoogleGeminiDirect() {
  try {
    console.log('测试直接 Google Gemini API...');
    
    const response = await fetch('http://localhost:3001/api/generate-ai-code-stream', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        prompt: 'Create a simple React component that displays "Hello World" with a blue background',
        model: 'google/gemini-2.5-pro', // 使用直接的 Google API
        context: {}
      })
    });
    
    console.log('响应状态:', response.status);
    
    if (!response.ok) {
      const errorData = await response.json();
      console.log('❌ 失败:', errorData);
      return false;
    }
    
    // 处理流式响应
    const reader = response.body?.getReader();
    const decoder = new TextDecoder();
    let generatedCode = '';
    let hasError = false;
    let statusMessages = [];
    
    if (reader) {
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;
        
        const chunk = decoder.decode(value);
        const lines = chunk.split('\n');
        
        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6));
              
              if (data.type === 'error') {
                console.log('❌ 流错误:', data.error);
                hasError = true;
              } else if (data.type === 'complete') {
                generatedCode = data.generatedCode;
                console.log('✅ 代码生成完成');
              } else if (data.type === 'status') {
                statusMessages.push(data.message);
                console.log('状态:', data.message);
              }
            } catch (e) {
              // 忽略解析错误
            }
          }
        }
      }
    }
    
    console.log('\n=== 测试结果 ===');
    console.log('状态消息:', statusMessages);
    console.log('有错误:', hasError);
    console.log('生成的代码长度:', generatedCode ? generatedCode.length : 0);
    
    if (hasError) {
      console.log('❌ 生成过程中有错误');
      return false;
    } else if (generatedCode && generatedCode.length > 100) {
      console.log('✅ 成功生成代码');
      console.log('代码预览:', generatedCode.substring(0, 300) + '...');
      return true;
    } else {
      console.log('⚠️ 生成完成但代码为空或太短');
      console.log('生成的代码:', generatedCode);
      return false;
    }
    
  } catch (error) {
    console.error('❌ 测试出错:', error.message);
    return false;
  }
}

// 运行测试
testGoogleGeminiDirect().then(success => {
  if (success) {
    console.log('\n🎉 Google Gemini API 工作正常！');
    console.log('现在您可以尝试克隆网站了。');
  } else {
    console.log('\n❌ Google Gemini API 仍有问题');
    console.log('请检查 API 密钥或稍后重试。');
  }
});
