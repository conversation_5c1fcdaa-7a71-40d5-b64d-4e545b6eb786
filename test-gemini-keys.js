// Test all Gemini API keys
const geminiKeys = [
  'AIzaSyC2PUEm6tjUzngQbXJjFhIvlUvIXJkwkZI',
  'AIzaSyB6D6XvNrQui4_G0XRCwEAC65ZjdKxoWJg', 
  'AIzaSyAa1Is-7flE0SvybnwdwDmd_xNW3TKk7dA',
  'AIzaSyBcQXlQFRzugqr-tqFJfG87ZSKfP6v60_s',
  'AIzaSyCi3QHli-QOFHD47sh7bd-vwKzxntliiCs'
];

async function testGeminiKey(apiKey, index) {
  try {
    console.log(`\n=== 测试密钥 ${index + 1}: ${apiKey.substring(0, 20)}... ===`);
    
    // Test with Google Generative AI directly
    const response = await fetch('https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=' + apiKey, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        contents: [{
          parts: [{
            text: 'Hello, please respond with "API key is working"'
          }]
        }]
      })
    });
    
    console.log('响应状态:', response.status);
    
    if (response.ok) {
      const data = await response.json();
      if (data.candidates && data.candidates[0] && data.candidates[0].content) {
        console.log('✅ 密钥有效');
        console.log('响应:', data.candidates[0].content.parts[0].text.substring(0, 100));
        return { valid: true, key: apiKey, response: data };
      } else {
        console.log('⚠️ 响应格式异常');
        return { valid: false, key: apiKey, error: 'Invalid response format' };
      }
    } else {
      const errorData = await response.text();
      console.log('❌ 密钥无效');
      console.log('错误:', errorData.substring(0, 200));
      return { valid: false, key: apiKey, error: errorData };
    }
    
  } catch (error) {
    console.error('❌ 测试出错:', error.message);
    return { valid: false, key: apiKey, error: error.message };
  }
}

async function testAllGeminiKeys() {
  console.log('开始测试所有 Gemini API 密钥...\n');
  
  const results = [];
  
  for (let i = 0; i < geminiKeys.length; i++) {
    const result = await testGeminiKey(geminiKeys[i], i);
    results.push(result);
    
    // 等待一下避免请求过快
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  console.log('\n=== 测试结果汇总 ===');
  const validKeys = results.filter(r => r.valid);
  const invalidKeys = results.filter(r => !r.valid);
  
  console.log(`✅ 有效密钥: ${validKeys.length} 个`);
  validKeys.forEach((result, index) => {
    console.log(`  ${index + 1}. ${result.key.substring(0, 20)}...`);
  });
  
  console.log(`❌ 无效密钥: ${invalidKeys.length} 个`);
  invalidKeys.forEach((result, index) => {
    console.log(`  ${index + 1}. ${result.key.substring(0, 20)}... (${result.error.substring(0, 50)}...)`);
  });
  
  if (validKeys.length > 0) {
    console.log(`\n推荐使用第一个有效密钥: ${validKeys[0].key}`);
    return validKeys;
  } else {
    console.log('\n❌ 所有密钥都无效');
    return [];
  }
}

async function testWithOurAPI(apiKey) {
  try {
    console.log(`\n=== 测试密钥在我们的 API 中的工作情况 ===`);
    
    // 先更新环境变量（需要重启服务器）
    console.log('注意：需要重启开发服务器以应用新的 API 密钥');
    
    const response = await fetch('http://localhost:3001/api/generate-ai-code-stream', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        prompt: 'Create a simple React component that displays "Hello World"',
        model: 'google/gemini-2.5-pro',
        context: {}
      })
    });
    
    console.log('API 响应状态:', response.status);
    
    if (!response.ok) {
      const errorData = await response.json();
      console.log('❌ API 调用失败:', errorData);
      return false;
    }
    
    // Handle streaming response
    const reader = response.body?.getReader();
    const decoder = new TextDecoder();
    let hasCode = false;
    let hasError = false;
    
    if (reader) {
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;
        
        const chunk = decoder.decode(value);
        const lines = chunk.split('\n');
        
        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6));
              
              if (data.type === 'error') {
                console.log('❌ 流错误:', data.error);
                hasError = true;
              } else if (data.type === 'complete' && data.generatedCode) {
                hasCode = true;
                console.log('✅ 代码生成成功');
              } else if (data.type === 'status') {
                console.log('状态:', data.message);
              }
            } catch (e) {
              // Ignore parse errors
            }
          }
        }
      }
    }
    
    if (hasError) {
      console.log('❌ API 调用有错误');
      return false;
    } else if (hasCode) {
      console.log('✅ API 调用成功');
      return true;
    } else {
      console.log('⚠️ API 调用完成但没有生成代码');
      return false;
    }
    
  } catch (error) {
    console.error('❌ API 测试出错:', error.message);
    return false;
  }
}

// 运行测试
testAllGeminiKeys().then(validKeys => {
  if (validKeys.length > 0) {
    console.log('\n现在测试第一个有效密钥在我们的 API 中的工作情况...');
    // 注意：这需要重启服务器才能生效
    testWithOurAPI(validKeys[0].key);
  }
});
