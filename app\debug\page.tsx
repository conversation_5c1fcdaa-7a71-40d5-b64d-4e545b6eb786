'use client';

import { useState } from 'react';

export default function DebugPage() {
  const [testResults, setTestResults] = useState<any>({});
  const [loading, setLoading] = useState(false);

  const testAPI = async (endpoint: string, body: any) => {
    try {
      console.log(`Testing ${endpoint} with body:`, body);
      const response = await fetch(endpoint, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(body)
      });
      
      const data = await response.json();
      console.log(`${endpoint} response:`, data);
      
      return {
        success: response.ok,
        status: response.status,
        data: data
      };
    } catch (error) {
      console.error(`${endpoint} error:`, error);
      return {
        success: false,
        error: error.message
      };
    }
  };

  const runDiagnostics = async () => {
    setLoading(true);
    const results: any = {};

    // Test 1: Environment variables
    console.log('Testing environment variables...');
    results.env = {
      hasFirecrawlKey: !!process.env.FIRECRAWL_API_KEY,
      hasE2BKey: !!process.env.E2B_API_KEY,
      hasOpenAIKey: !!process.env.OPENAI_API_KEY
    };

    // Test 2: Scrape URL Enhanced
    console.log('Testing scrape-url-enhanced...');
    results.scrape = await testAPI('/api/scrape-url-enhanced', {
      url: 'https://example.com'
    });

    // Test 3: Create Sandbox
    console.log('Testing create-ai-sandbox...');
    results.sandbox = await testAPI('/api/create-ai-sandbox', {});

    // Test 4: Generate AI Code (simple test)
    if (results.sandbox.success) {
      console.log('Testing generate-ai-code-stream...');
      try {
        const response = await fetch('/api/generate-ai-code-stream', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            prompt: 'Create a simple React component that says "Hello World"',
            model: 'openai/gemini-2.5-pro',
            context: {}
          })
        });

        if (response.ok) {
          const reader = response.body?.getReader();
          const decoder = new TextDecoder();
          let generatedCode = '';

          if (reader) {
            while (true) {
              const { done, value } = await reader.read();
              if (done) break;
              
              const chunk = decoder.decode(value);
              const lines = chunk.split('\n');
              
              for (const line of lines) {
                if (line.startsWith('data: ')) {
                  try {
                    const data = JSON.parse(line.slice(6));
                    if (data.type === 'complete') {
                      generatedCode = data.generatedCode;
                    }
                  } catch (e) {
                    // Ignore parse errors
                  }
                }
              }
            }
          }

          results.generate = {
            success: true,
            hasCode: !!generatedCode,
            codeLength: generatedCode.length
          };
        } else {
          results.generate = {
            success: false,
            status: response.status
          };
        }
      } catch (error) {
        results.generate = {
          success: false,
          error: error.message
        };
      }
    }

    setTestResults(results);
    setLoading(false);
  };

  return (
    <div className="min-h-screen bg-gray-900 text-white p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">Open Lovable 诊断工具</h1>
        
        <button
          onClick={runDiagnostics}
          disabled={loading}
          className="bg-blue-600 hover:bg-blue-700 px-6 py-3 rounded-lg font-medium mb-8 disabled:opacity-50"
        >
          {loading ? '运行诊断中...' : '运行诊断测试'}
        </button>

        {Object.keys(testResults).length > 0 && (
          <div className="space-y-6">
            <div className="bg-gray-800 p-6 rounded-lg">
              <h2 className="text-xl font-semibold mb-4">环境变量检查</h2>
              <div className="space-y-2">
                <div className={`flex items-center gap-2 ${testResults.env?.hasFirecrawlKey ? 'text-green-400' : 'text-red-400'}`}>
                  <span>{testResults.env?.hasFirecrawlKey ? '✓' : '✗'}</span>
                  <span>Firecrawl API Key</span>
                </div>
                <div className={`flex items-center gap-2 ${testResults.env?.hasE2BKey ? 'text-green-400' : 'text-red-400'}`}>
                  <span>{testResults.env?.hasE2BKey ? '✓' : '✗'}</span>
                  <span>E2B API Key</span>
                </div>
                <div className={`flex items-center gap-2 ${testResults.env?.hasOpenAIKey ? 'text-green-400' : 'text-red-400'}`}>
                  <span>{testResults.env?.hasOpenAIKey ? '✓' : '✗'}</span>
                  <span>OpenAI API Key</span>
                </div>
              </div>
            </div>

            <div className="bg-gray-800 p-6 rounded-lg">
              <h2 className="text-xl font-semibold mb-4">网站抓取测试</h2>
              <div className={`flex items-center gap-2 ${testResults.scrape?.success ? 'text-green-400' : 'text-red-400'}`}>
                <span>{testResults.scrape?.success ? '✓' : '✗'}</span>
                <span>
                  {testResults.scrape?.success 
                    ? `成功 (状态: ${testResults.scrape.status})`
                    : `失败: ${testResults.scrape?.error || testResults.scrape?.data?.error || '未知错误'}`
                  }
                </span>
              </div>
              {testResults.scrape?.data && (
                <pre className="mt-4 bg-gray-900 p-4 rounded text-sm overflow-auto max-h-40">
                  {JSON.stringify(testResults.scrape.data, null, 2)}
                </pre>
              )}
            </div>

            <div className="bg-gray-800 p-6 rounded-lg">
              <h2 className="text-xl font-semibold mb-4">沙箱创建测试</h2>
              <div className={`flex items-center gap-2 ${testResults.sandbox?.success ? 'text-green-400' : 'text-red-400'}`}>
                <span>{testResults.sandbox?.success ? '✓' : '✗'}</span>
                <span>
                  {testResults.sandbox?.success 
                    ? `成功 (状态: ${testResults.sandbox.status})`
                    : `失败: ${testResults.sandbox?.error || testResults.sandbox?.data?.error || '未知错误'}`
                  }
                </span>
              </div>
              {testResults.sandbox?.data && (
                <pre className="mt-4 bg-gray-900 p-4 rounded text-sm overflow-auto max-h-40">
                  {JSON.stringify(testResults.sandbox.data, null, 2)}
                </pre>
              )}
            </div>

            {testResults.generate && (
              <div className="bg-gray-800 p-6 rounded-lg">
                <h2 className="text-xl font-semibold mb-4">AI 代码生成测试</h2>
                <div className={`flex items-center gap-2 ${testResults.generate?.success ? 'text-green-400' : 'text-red-400'}`}>
                  <span>{testResults.generate?.success ? '✓' : '✗'}</span>
                  <span>
                    {testResults.generate?.success 
                      ? `成功 (生成了 ${testResults.generate.codeLength} 字符的代码)`
                      : `失败: ${testResults.generate?.error || '未知错误'}`
                    }
                  </span>
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
