// 测试沙箱状态
async function testSandboxStatus() {
  try {
    console.log('检查沙箱状态...');
    
    const response = await fetch('http://localhost:3001/api/sandbox-status', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    console.log('沙箱状态 API 响应状态:', response.status);
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ 沙箱状态获取成功');
      console.log('沙箱数据:', JSON.stringify(data, null, 2));
      
      if (data.sandboxId) {
        console.log(`\n🔗 沙箱 URL: https://5173-${data.sandboxId}.e2b.app`);
        
        // 测试直接访问沙箱 URL
        console.log('\n测试直接访问沙箱...');
        try {
          const sandboxResponse = await fetch(`https://5173-${data.sandboxId}.e2b.app`);
          console.log('沙箱直接访问状态:', sandboxResponse.status);
          
          if (sandboxResponse.ok) {
            const html = await sandboxResponse.text();
            console.log('沙箱内容长度:', html.length);
            
            if (html.includes('Vite + React')) {
              console.log('✅ 沙箱显示 Vite + React 默认页面');
            } else if (html.includes('Nano Banana')) {
              console.log('✅ 沙箱显示生成的内容');
            } else if (html.includes('Sandbox Ready')) {
              console.log('⚠️ 沙箱显示准备就绪页面，但没有应用内容');
            } else {
              console.log('❓ 沙箱显示未知内容');
              console.log('内容预览:', html.substring(0, 200) + '...');
            }
          } else {
            console.log('❌ 无法访问沙箱');
          }
        } catch (error) {
          console.log('❌ 沙箱访问出错:', error.message);
        }
      } else {
        console.log('⚠️ 没有活跃的沙箱');
      }
      
      return true;
    } else {
      const errorData = await response.json();
      console.log('❌ 沙箱状态获取失败');
      console.log('错误数据:', errorData);
      return false;
    }
    
  } catch (error) {
    console.error('❌ 沙箱状态测试出错:', error.message);
    return false;
  }
}

async function testRestartVite() {
  try {
    console.log('\n尝试重启 Vite 服务器...');
    
    const response = await fetch('http://localhost:3001/api/restart-vite', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({})
    });
    
    console.log('重启 Vite 响应状态:', response.status);
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ Vite 重启成功');
      console.log('重启结果:', data);
      return true;
    } else {
      const errorData = await response.json();
      console.log('❌ Vite 重启失败');
      console.log('错误数据:', errorData);
      return false;
    }
    
  } catch (error) {
    console.error('❌ Vite 重启测试出错:', error.message);
    return false;
  }
}

async function runSandboxDiagnostics() {
  console.log('=== 沙箱诊断 ===\n');
  
  // 测试 1: 检查沙箱状态
  const statusCheck = await testSandboxStatus();
  
  // 等待一下
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  // 测试 2: 尝试重启 Vite
  const viteRestart = await testRestartVite();
  
  // 等待 Vite 重启
  await new Promise(resolve => setTimeout(resolve, 5000));
  
  // 测试 3: 再次检查状态
  console.log('\n=== 重启后再次检查 ===');
  const statusCheckAfter = await testSandboxStatus();
  
  console.log('\n=== 诊断结果汇总 ===');
  console.log(`沙箱状态检查: ${statusCheck ? '✅ 成功' : '❌ 失败'}`);
  console.log(`Vite 重启: ${viteRestart ? '✅ 成功' : '❌ 失败'}`);
  console.log(`重启后状态: ${statusCheckAfter ? '✅ 成功' : '❌ 失败'}`);
  
  if (!statusCheck) {
    console.log('\n🔧 建议的解决方案:');
    console.log('1. 检查是否有活跃的沙箱');
    console.log('2. 尝试创建新的沙箱');
    console.log('3. 检查 E2B 服务状态');
  } else if (!viteRestart) {
    console.log('\n🔧 Vite 重启失败，可能需要手动干预');
  } else {
    console.log('\n🎉 沙箱诊断完成！');
  }
}

// 运行诊断
runSandboxDiagnostics();
