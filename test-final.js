// 最终测试 - 使用 Gemini 2.5 模型
async function testGemini25() {
  try {
    console.log('测试 Gemini 2.5 模型...');
    
    const response = await fetch('http://localhost:3001/api/generate-ai-code-stream', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        prompt: 'Create a simple React component that displays "Hello World" with a blue background',
        model: 'google/gemini-2.5-pro', // 使用我们配置的模型
        context: {}
      })
    });
    
    console.log('响应状态:', response.status);
    
    if (!response.ok) {
      const errorData = await response.json();
      console.log('❌ 失败:', errorData);
      return false;
    }
    
    // 处理流式响应
    const reader = response.body?.getReader();
    const decoder = new TextDecoder();
    let generatedCode = '';
    let hasError = false;
    
    if (reader) {
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;
        
        const chunk = decoder.decode(value);
        const lines = chunk.split('\n');
        
        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6));
              
              if (data.type === 'error') {
                console.log('❌ 流错误:', data.error);
                hasError = true;
              } else if (data.type === 'complete') {
                generatedCode = data.generatedCode;
                console.log('✅ 代码生成完成');
              } else if (data.type === 'status') {
                console.log('状态:', data.message);
              }
            } catch (e) {
              // 忽略解析错误
            }
          }
        }
      }
    }
    
    if (hasError) {
      console.log('❌ 生成过程中有错误');
      return false;
    } else if (generatedCode && generatedCode.length > 100) {
      console.log('✅ 成功生成代码');
      console.log('代码长度:', generatedCode.length);
      console.log('代码预览:', generatedCode.substring(0, 200) + '...');
      return true;
    } else {
      console.log('⚠️ 生成完成但代码为空或太短');
      console.log('生成的代码:', generatedCode);
      return false;
    }
    
  } catch (error) {
    console.error('❌ 测试出错:', error.message);
    return false;
  }
}

async function testWebsiteCloning() {
  try {
    console.log('\n测试完整的网站克隆流程...');
    
    // 1. 测试网站抓取
    console.log('1. 测试网站抓取...');
    const scrapeResponse = await fetch('http://localhost:3001/api/scrape-url-enhanced', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ url: 'https://example.com' })
    });
    
    if (!scrapeResponse.ok) {
      console.log('❌ 网站抓取失败');
      return false;
    }
    
    const scrapeData = await scrapeResponse.json();
    if (!scrapeData.success) {
      console.log('❌ 网站抓取失败:', scrapeData.error);
      return false;
    }
    
    console.log('✅ 网站抓取成功');
    
    // 2. 测试沙箱创建
    console.log('2. 测试沙箱创建...');
    const sandboxResponse = await fetch('http://localhost:3001/api/create-ai-sandbox', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({})
    });
    
    if (!sandboxResponse.ok) {
      console.log('❌ 沙箱创建失败');
      return false;
    }
    
    const sandboxData = await sandboxResponse.json();
    if (!sandboxData.success) {
      console.log('❌ 沙箱创建失败:', sandboxData.error);
      return false;
    }
    
    console.log('✅ 沙箱创建成功');
    console.log('沙箱 URL:', sandboxData.url);
    
    // 3. 测试 AI 代码生成
    console.log('3. 测试 AI 代码生成...');
    const aiSuccess = await testGemini25();
    
    if (aiSuccess) {
      console.log('\n🎉 所有测试通过！网站克隆功能应该可以正常工作了。');
      console.log('\n现在您可以：');
      console.log('1. 访问 http://localhost:3001');
      console.log('2. 输入任何网址');
      console.log('3. 点击生成按钮');
      console.log('4. 等待 AI 生成 React 代码');
      return true;
    } else {
      console.log('❌ AI 代码生成失败');
      return false;
    }
    
  } catch (error) {
    console.error('❌ 完整测试出错:', error.message);
    return false;
  }
}

// 运行测试
testWebsiteCloning();
