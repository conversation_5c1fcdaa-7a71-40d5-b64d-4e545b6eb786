// Simple test script for API endpoints

async function testScrapeAPI() {
  try {
    console.log('Testing scrape-url-enhanced API...');
    
    const response = await fetch('http://localhost:3001/api/scrape-url-enhanced', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        url: 'https://example.com'
      })
    });
    
    console.log('Response status:', response.status);
    console.log('Response headers:', Object.fromEntries(response.headers));
    
    const data = await response.json();
    console.log('Response data:', data);
    
    if (data.success) {
      console.log('✅ Scrape API test passed');
    } else {
      console.log('❌ Scrape API test failed:', data.error);
    }
    
  } catch (error) {
    console.error('❌ Scrape API test error:', error.message);
  }
}

async function testSandboxAPI() {
  try {
    console.log('\nTesting create-ai-sandbox API...');
    
    const response = await fetch('http://localhost:3001/api/create-ai-sandbox', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({})
    });
    
    console.log('Response status:', response.status);
    
    const data = await response.json();
    console.log('Response data:', data);
    
    if (data.success || data.sandboxId) {
      console.log('✅ Sandbox API test passed');
    } else {
      console.log('❌ Sandbox API test failed:', data.error);
    }
    
  } catch (error) {
    console.error('❌ Sandbox API test error:', error.message);
  }
}

async function runTests() {
  await testScrapeAPI();
  await testSandboxAPI();
}

runTests();
