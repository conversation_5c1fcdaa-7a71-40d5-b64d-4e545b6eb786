// 测试 E2B 连接
async function testE2BConnection() {
  try {
    console.log('测试 E2B API 连接...');
    
    // 测试基本的 E2B API 连接
    const response = await fetch('https://api.e2b.app/sandboxes', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${process.env.E2B_API_KEY || 'e2b_6d0dd8836c08f904686554722b214e2af2c9f19f'}`,
        'Content-Type': 'application/json'
      }
    });
    
    console.log('E2B API 响应状态:', response.status);
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ E2B API 连接成功');
      console.log('响应数据:', data);
      return true;
    } else {
      const errorText = await response.text();
      console.log('❌ E2B API 连接失败');
      console.log('错误响应:', errorText);
      return false;
    }
    
  } catch (error) {
    console.error('❌ E2B 连接测试出错:', error.message);
    console.error('错误详情:', error);
    return false;
  }
}

async function testCreateSandboxAPI() {
  try {
    console.log('\n测试创建沙箱 API...');
    
    const response = await fetch('http://localhost:3001/api/create-ai-sandbox', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({})
    });
    
    console.log('创建沙箱 API 响应状态:', response.status);
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ 沙箱创建成功');
      console.log('沙箱数据:', data);
      return true;
    } else {
      const errorData = await response.json();
      console.log('❌ 沙箱创建失败');
      console.log('错误数据:', errorData);
      return false;
    }
    
  } catch (error) {
    console.error('❌ 沙箱创建测试出错:', error.message);
    return false;
  }
}

async function runE2BTests() {
  console.log('=== E2B 连接诊断 ===\n');
  
  // 测试 1: 直接 E2B API 连接
  const directConnection = await testE2BConnection();
  
  // 等待一下
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  // 测试 2: 通过我们的 API 创建沙箱
  const sandboxCreation = await testCreateSandboxAPI();
  
  console.log('\n=== 测试结果汇总 ===');
  console.log(`直接 E2B API 连接: ${directConnection ? '✅ 成功' : '❌ 失败'}`);
  console.log(`沙箱创建 API: ${sandboxCreation ? '✅ 成功' : '❌ 失败'}`);
  
  if (!directConnection && !sandboxCreation) {
    console.log('\n🔧 建议的解决方案:');
    console.log('1. 检查网络连接');
    console.log('2. 检查 E2B API 密钥是否有效');
    console.log('3. 稍后重试（可能是临时的网络问题）');
    console.log('4. 检查防火墙设置');
  } else if (directConnection && !sandboxCreation) {
    console.log('\n🔧 E2B API 可用，但沙箱创建失败，可能是代码问题');
  } else if (!directConnection && sandboxCreation) {
    console.log('\n🔧 沙箱创建成功，直接 API 测试失败可能是认证问题');
  } else {
    console.log('\n🎉 所有测试通过！E2B 连接正常。');
  }
}

// 运行测试
runE2BTests();
