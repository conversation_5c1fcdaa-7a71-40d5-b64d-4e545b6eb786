// Test different Gemini models with the keys
const geminiKeys = [
  'AIzaSyC2PUEm6tjUzngQbXJjFhIvlUvIXJkwkZI',
  'AIzaSyB6D6XvNrQui4_G0XRCwEAC65ZjdKxoWJg', 
  'AIzaSyAa1Is-7flE0SvybnwdwDmd_xNW3TKk7dA',
  'AIzaSyBcQXlQFRzugqr-tqFJfG87ZSKfP6v60_s',
  'AIzaSyCi3QHli-QOFHD47sh7bd-vwKzxntliiCs'
];

const models = [
  'gemini-1.5-flash',
  'gemini-1.5-pro',
  'gemini-pro'
];

async function testKeyWithModel(apiKey, model, keyIndex) {
  try {
    console.log(`测试密钥 ${keyIndex + 1} 与模型 ${model}...`);
    
    const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/${model}:generateContent?key=${apiKey}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        contents: [{
          parts: [{
            text: 'Say "Hello" in one word'
          }]
        }]
      })
    });
    
    if (response.ok) {
      const data = await response.json();
      if (data.candidates && data.candidates[0] && data.candidates[0].content) {
        console.log(`✅ 成功 - 密钥 ${keyIndex + 1} + ${model}`);
        return { success: true, key: apiKey, model: model };
      }
    } else {
      const errorText = await response.text();
      const error = JSON.parse(errorText);
      if (error.error.code === 503) {
        console.log(`⏳ 模型过载 - 密钥 ${keyIndex + 1} + ${model}`);
      } else if (error.error.code === 400 && error.error.message.includes('expired')) {
        console.log(`❌ 密钥过期 - 密钥 ${keyIndex + 1}`);
        return { success: false, key: apiKey, expired: true };
      } else {
        console.log(`❌ 其他错误 - 密钥 ${keyIndex + 1} + ${model}: ${error.error.message}`);
      }
    }
    
    return { success: false, key: apiKey, model: model };
    
  } catch (error) {
    console.log(`❌ 网络错误 - 密钥 ${keyIndex + 1} + ${model}: ${error.message}`);
    return { success: false, key: apiKey, model: model, networkError: true };
  }
}

async function findWorkingCombination() {
  console.log('寻找可用的密钥和模型组合...\n');
  
  for (let keyIndex = 0; keyIndex < geminiKeys.length; keyIndex++) {
    const key = geminiKeys[keyIndex];
    console.log(`\n=== 测试密钥 ${keyIndex + 1}: ${key.substring(0, 20)}... ===`);
    
    for (const model of models) {
      const result = await testKeyWithModel(key, model, keyIndex);
      
      if (result.success) {
        console.log(`\n🎉 找到可用组合！`);
        console.log(`密钥: ${key}`);
        console.log(`模型: ${model}`);
        return { key, model };
      }
      
      if (result.expired) {
        console.log(`跳过已过期的密钥 ${keyIndex + 1}`);
        break; // 如果密钥过期，跳过这个密钥的其他模型测试
      }
      
      // 等待一下避免请求过快
      await new Promise(resolve => setTimeout(resolve, 500));
    }
  }
  
  console.log('\n❌ 没有找到可用的密钥和模型组合');
  return null;
}

// 运行测试
findWorkingCombination().then(result => {
  if (result) {
    console.log('\n建议配置:');
    console.log(`GEMINI_API_KEY=${result.key}`);
    console.log(`推荐模型: google/gemini-2.5-pro 或 google/gemini-1.5-flash`);
  } else {
    console.log('\n建议:');
    console.log('1. 等待几分钟后重试（可能是临时过载）');
    console.log('2. 检查 Google AI Studio 获取新的 API 密钥');
    console.log('3. 或者配置其他 AI 提供商的密钥（OpenAI、Anthropic 等）');
  }
});
